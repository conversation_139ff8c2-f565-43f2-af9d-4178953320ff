import {
  AUTO_STYLE,
  AnimationBuilder,
  AnimationFactory,
  AnimationGroupPlayer,
  AnimationMetadataType,
  BrowserAnimationBuilder,
  NoopAnimationPlayer,
  animate,
  animateChild,
  animation,
  group,
  keyframes,
  query,
  sequence,
  stagger,
  state,
  style,
  transition,
  trigger,
  useAnimation,
  ɵPRE_STYLE
} from "./chunk-UMMBKHFS.js";
import "./chunk-NHFQGQIO.js";
import "./chunk-HAIABML7.js";
import "./chunk-P6U2JBMQ.js";
import "./chunk-S35DAJRX.js";
export {
  AUTO_STYLE,
  AnimationBuilder,
  AnimationFactory,
  AnimationMetadataType,
  NoopAnimationPlayer,
  animate,
  animateChild,
  animation,
  group,
  keyframes,
  query,
  sequence,
  stagger,
  state,
  style,
  transition,
  trigger,
  useAnimation,
  AnimationGroupPlayer as ɵAnimationGroupPlayer,
  BrowserAnimationBuilder as ɵBrowserAnimationBuilder,
  ɵPRE_STYLE
};
//# sourceMappingURL=@angular_animations.js.map
