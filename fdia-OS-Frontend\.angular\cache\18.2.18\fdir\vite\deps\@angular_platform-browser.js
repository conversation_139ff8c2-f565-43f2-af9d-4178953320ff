import {
  BrowserDom<PERSON><PERSON>pter,
  BrowserGetTestability,
  BrowserModule,
  By,
  DomEventsPlugin,
  DomRendererFactory2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mpl,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  INTERNAL_BROWSER_PLATFORM_PROVIDERS,
  KeyEventsPlugin,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  initDomAdapter,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withNoHttpTransferCache
} from "./chunk-VO7BZHO5.js";
import "./chunk-4NVJJXK3.js";
import {
  getDOM
} from "./chunk-NHFQGQIO.js";
import "./chunk-HAIABML7.js";
import "./chunk-P6U2JBMQ.js";
import "./chunk-S35DAJRX.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM,
  initDomAdapter as ɵinitDomAdapter
};
//# sourceMappingURL=@angular_platform-browser.js.map
