import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';

import { setPasswordGuard } from './set-password.guard';

describe('setPasswordGuard', () => {
  const executeGuard: CanActivateFn = (...guardParameters) => 
      TestBed.runInInjectionContext(() => setPasswordGuard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
