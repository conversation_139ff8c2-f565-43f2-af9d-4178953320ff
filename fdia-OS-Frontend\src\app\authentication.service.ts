// authentication.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AuthenticationService {

  private apiUrl = 'http://localhost:5553/auth/login';
  private profileUrl = 'http://localhost:5553/api/signon-profile/';
  private logoutUrl = 'http://localhost:5553/auth/logout'; // Logout endpoint
  private setPasswordUrl = 'http://localhost:5553/auth/set-password'; // Set Password endpoint

  constructor(private http: HttpClient) { }

  login(credentials: any): Observable<any> {
    return this.http.post(this.apiUrl, credentials, { withCredentials: true });
  }

  getProfile(username: string): Observable<any> {
    return this.http.get(`${this.profileUrl}${username}`, { withCredentials: true });
  }

  // New logout method
  logout(): Observable<any> {
    return this.http.post(this.logoutUrl, {}, { responseType: 'text' as 'json', withCredentials: true });
  }
  setPassword(data: { username: string; password: string; confirmPassword: string }): Observable<any> {
    return this.http.post(this.setPasswordUrl, data);
  }
}
