<div *ngIf="showSuccessPopup" class="popup">
  <div class="popup-content">
    <span class="close" (click)="closeSuccessPopup()">&times;</span>
    <p>Record Inserted Successfully</p>
  </div>
</div>

<div *ngIf="!submissionSuccess; else successMessage">
  <div *ngIf="showInitialInput" class="initial-input">
    <form [formGroup]="form" class="form-container">
      <div class="form-main-field">
        <label for="ID" class="form-label">ID</label>
        <div class="input-button-group">
          <input formControlName="ID" id="ID" type="text" class="form-input" required />

          <!-- زر الإضافة -->
          <button type="button" (click)="loadDataAndBuildForm()" [disabled]="isLoading" class="action-button" title="Add">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/add.svg" alt="Add Icon" class="icon" />
              </div>

            </div>
          </button>

          <!-- زر التعديل -->
          <button type="button" (click)="viewData()" [disabled]="isLoading" class="action-button" title="Edit">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/edit.svg" alt="Edit Icon" class="icon" />
              </div>

            </div>
          </button>
        </div>
      </div>
    </form>
  </div>

  <div *ngIf="errorMessage" class="error-message">{{ errorMessage }}</div>

  <div *ngIf="!showInitialInput" class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="horizontal-container">
      <div class="form-field">
        <!-- <label>ID</label> -->
        <p>{{ form.get('ID')?.value }}</p>
      </div>

      <div class="button-group">

         

          <!-- submit button -->
        <button type="submit" [disabled]="isViewMode" class="action-button" title="Submit">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/Submit.svg" alt="Submit Icon" class="icon" />
              </div>

            </div>
          </button>

          <!-- Validate button -->
        <button type="button" (click)="validateRecord()"  class="green-button" title="Validate">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/Validate.svg" alt="Add Validate" class="icon" />
              </div>
            </div>
          </button>

           <!-- Authorize button -->
        <button type="button" (click)="authorizeRecord()"  class="green-button" title="Authorize">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/Auth.svg" alt="Add Authorize" class="icon" />
              </div>
            </div>
          </button>

          <!-- back button -->
        <button type="button" (click)="goBack()"  class="black-button" title="Back">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/Back.svg" alt="Add Icon" class="icon" />
              </div>

            </div>
          </button>

          <!-- Reject button -->
        <button type="button"   class="red-button" title="Reject">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/Reject.svg" alt="Add Reject" class="icon" />
              </div>
            </div>
          </button>

           <!-- Delete button -->
        <button type="button"   class="red-button" title="Delete">
            <div class="action-icon">
              <div class="icon-frame">
                <img src="assets/images/Delete.svg" alt="Add Delete" class="icon" />
              </div>
            </div>
          </button>
        <!-- <button type="button" (click)="goBack()" class="action-button" title="Back"><i class="fa-sharp-duotone fa-solid fa-arrow-up-right-from-square"></i></button>
        <button type="button" (click)="authorizeRecord()" class="action-button" title="Authorize"><i class="fa-sharp-duotone fa-solid fa-wand-sparkles"></i></button>
        <button type="submit" [disabled]="isViewMode" class="submit-button" title="Submit">Submit</button>
        <button type="button" (click)="validateRecord()" class="validate-button" title="Validate">Validate</button> -->
        <div *ngIf="errorMessage" class="error-message">{{ errorMessage }}</div>
      </div>

      
    </div>
      <!-- 🔹 الحقول التي ليست داخل Group -->
      <div class="form-fields-grid">
        <ng-container *ngFor="let field of fields; let i = index; trackBy: trackByFieldName">

          <!-- ✅ الحقول العادية أو متعددة القيم (خارج Group وبدون ID) -->
          <div *ngIf="!field.Group && field.fieldName?.toUpperCase() !== 'ID'" class="form-field">

            <!-- 🔸 حقل عادي -->
            <ng-container *ngIf="!field.isMulti">
              <label [for]="field.fieldName">{{ field.fieldName }} <span *ngIf="field.mandatory">*</span></label>

              <input *ngIf="field.type === 'boolean'" [formControlName]="field.fieldName" [id]="field.fieldName"
                type="checkbox" [readonly]="isViewMode || field.noInput" />
              <input *ngIf="field.type === 'string'" [formControlName]="field.fieldName" [id]="field.fieldName"
                type="text" [readonly]="isViewMode || field.noInput" [placeholder]="field.fieldName" />
              <input *ngIf="field.type === 'int'" [formControlName]="field.fieldName" [id]="field.fieldName"
                type="number" [readonly]="isViewMode || field.noInput" />
              <input *ngIf="field.type === 'date'" [formControlName]="field.fieldName" [id]="field.fieldName"
                type="date" [readonly]="isViewMode || field.noInput" />
              <input *ngIf="field.type === 'double'" [formControlName]="field.fieldName" [id]="field.fieldName"
                type="number" step="00.50" [readonly]="isViewMode || field.noInput" />

              <select *ngIf="field.foreginKey" [formControlName]="field.fieldName" [id]="field.fieldName"
                (focus)="loadDropdownOptions(field.fieldName, field.foreginKey)"
                (change)="onDropdownChange($event, field.fieldName, i, undefined, field.Group)"
                [disabled]="isViewMode || field.noInput">
                <option *ngFor="let option of dropdownOptions[field.fieldName]" [value]="option.ROW_ID">
                  <ng-container *ngFor="let key of getKeys(option)">
                    {{ option[key] }}&nbsp;
                  </ng-container>
                </option>
              </select>
            </ng-container>

            <!-- 🔸 حقل متعدد القيم (isMulti) خارج Group -->
            <ng-container *ngIf="field.isMulti" [formArrayName]="field.fieldName">
              <ng-container *ngFor="let control of getMultiArray(field.fieldName).controls; let j = index">
                <div [formGroupName]="j" class="form-field is-multi">
                  <label>{{ field.fieldName }} ({{ j + 1 }})</label>

                  <div class="multi-input-container">

                    <!-- ✅ حقل الإدخال -->
                    <div class="multi-input">
                      <input *ngIf="field.type === 'string'" [formControlName]="field.fieldName" type="text"
                        [placeholder]="field.fieldName + '-' + (j+1)" />
                      <input *ngIf="field.type === 'int'" [formControlName]="field.fieldName" type="number" />
                      <input *ngIf="field.type === 'boolean'" [formControlName]="field.fieldName" type="checkbox" />
                      <input *ngIf="field.type === 'date'" [formControlName]="field.fieldName" type="date" />
                      <input *ngIf="field.type === 'double'" [formControlName]="field.fieldName" type="number"
                        step="00.50" />

                      <select *ngIf="field.foreginKey" [formControlName]="field.fieldName"
                        (focus)="loadDropdownOptions(field.fieldName, field.foreginKey)"
                        (change)="onDropdownChange($event, field.fieldName, undefined, j)">
                        <option *ngFor="let option of dropdownOptions[field.fieldName]" [value]="option.ROW_ID">
                          <ng-container *ngFor="let key of getKeys(option)">
                            {{ option[key] }}&nbsp;
                          </ng-container>
                        </option>
                      </select>
                    </div>

                    <!-- ✅ أزرار الإضافة والحذف -->
                    <div class="multi-buttons">
                      <!-- <button type="button" (click)="removeMultiField(field.fieldName, j)" class="remove-button"
            *ngIf="getMultiArray(field.fieldName).controls.length > 1 && !isViewMode">
            <i class="fas fa-trash-alt"></i>
          </button> -->
                      <button type="button" (click)="removeMultiField(field.fieldName, j)" class="remove-button"
                        [class.invisible]="getMultiArray(field.fieldName).length <= 1 || isViewMode">
                        <i class="fas fa-trash-alt"></i>
                      </button>

                      <button type="button" (click)="addMultiField(field, j)" class="add-button" *ngIf="!isViewMode">
                        <i class="fa-solid fa-plus"></i>
                      </button>
                    </div>

                  </div>
                </div>
              </ng-container>
            </ng-container>


          </div>

        </ng-container>
      </div>


      <!-- 🔹 الحقول داخل Group -->
      <div *ngFor="let field of fields; let i = index; trackBy: trackByFieldName">
        <div *ngIf="field.Group && isFirstFieldInGroup(field)" [formArrayName]="field.Group">
          <h3>{{ field.Group }}</h3>
          <ng-container *ngFor="let group of getGroupArray(field.Group).controls; let k = index">
            <div [formGroupName]="k" class="form-grid multi-field">
              <div class="group-fields">
                <div *ngFor="let groupField of getFieldsForGroup(field.Group)">
                  <!-- نفس منطقك الحالي للحقول داخل الجروب -->
                  <!-- لا تغييرات هنا -->

                  <div *ngIf="!groupField.isMulti" class="form-field">
                    <label>{{ groupField.fieldName }} <span *ngIf="groupField.mandatory">*</span></label>
                    <input *ngIf="groupField.type === 'string'" [formControlName]="groupField.fieldName" type="text"
                      [placeholder]="groupField.fieldName" />
                    <input *ngIf="groupField.type === 'int'" [formControlName]="groupField.fieldName" type="number" />
                    <input *ngIf="groupField.type === 'boolean'" [formControlName]="groupField.fieldName"
                      type="checkbox" />
                    <input *ngIf="groupField.type === 'date'" [formControlName]="groupField.fieldName" type="date" />
                    <input *ngIf="groupField.type === 'double'" [formControlName]="groupField.fieldName" type="number"
                      step="00.50" />

                    <select *ngIf="groupField.foreginKey" [formControlName]="groupField.fieldName"
                      (focus)="loadDropdownOptions(groupField.fieldName, groupField.foreginKey)"
                      (change)="onDropdownChange($event, groupField.fieldName, k, undefined, field.Group)">
                      <option *ngFor="let option of dropdownOptions[groupField.fieldName]" [value]="option.ROW_ID">
                        <ng-container *ngFor="let key of getKeys(option)">
                          {{ option[key] }}&nbsp;
                        </ng-container>
                      </option>
                    </select>
                  </div>

                  <!-- <div *ngIf="groupField.isMulti" [formArrayName]="groupField.fieldName">
                <h4>{{ groupField.fieldName }}</h4>
                <ng-container *ngFor="let multiControl of getMultiArray(groupField.fieldName).controls; let l = index">
                  <div [formGroupName]="l" class="form-field">
                    <label>{{ groupField.fieldName }} ({{ l }})</label>
                    <input *ngIf="groupField.type === 'string'" [formControlName]="groupField.fieldName" type="text" [placeholder]="groupField.fieldName"/>
                    <input *ngIf="groupField.type === 'int'" [formControlName]="groupField.fieldName" type="number" />
                    <input *ngIf="groupField.type === 'boolean'" [formControlName]="groupField.fieldName" type="checkbox" />
                    <input *ngIf="groupField.type === 'date'" [formControlName]="groupField.fieldName" type="date" />
                    <input *ngIf="groupField.type === 'double'" [formControlName]="groupField.fieldName" type="number" step="00.50" />

                    <select *ngIf="groupField.foreginKey" [formControlName]="groupField.fieldName" (focus)="loadDropdownOptions(groupField.fieldName, groupField.foreginKey)" (change)="onDropdownChange($event, groupField.fieldName, k, l, field.Group)">
                      <option *ngFor="let option of dropdownOptions[groupField.fieldName]" [value]="option.ROW_ID">
                        <ng-container *ngFor="let key of getKeys(option)">
                          {{ option[key] }}&nbsp;
                        </ng-container>
                      </option>
                    </select>

                    <button type="button" (click)="removeMultiField(groupField.fieldName, l)" class="remove-button" *ngIf="getMultiArray(groupField.fieldName).controls.length > 1 && !isViewMode">
                      <i class="fas fa-trash-alt"></i>
                    </button>
                    <button type="button" (click)="addMultiField(groupField, k)" class="add-button" *ngIf="!isViewMode">
                      <i class="fas fa-plus-circle"></i>
                    </button>
                  </div>
                </ng-container>
              </div> -->


                  <div *ngIf="groupField.isMulti" [formArrayName]="groupField.fieldName">
                    <h4>{{ groupField.fieldName }}</h4>

                    <ng-container
                      *ngFor="let multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; let l = index">
                      <div [formGroupName]="l" class="form-field">
                        <label>{{ groupField.fieldName }} ({{ l + 1 }})</label>

                        <input *ngIf="groupField.type === 'string'" [formControlName]="groupField.fieldName" type="text"
                          [placeholder]="groupField.fieldName + ' (' + (l + 1) + ')'" />

                        <input *ngIf="groupField.type === 'int'" [formControlName]="groupField.fieldName"
                          type="number" />

                        <input *ngIf="groupField.type === 'boolean'" [formControlName]="groupField.fieldName"
                          type="checkbox" />

                        <input *ngIf="groupField.type === 'date'" [formControlName]="groupField.fieldName"
                          type="date" />

                        <input *ngIf="groupField.type === 'double'" [formControlName]="groupField.fieldName"
                          type="number" step="0.01" />

                        <select *ngIf="groupField.foreginKey" [formControlName]="groupField.fieldName"
                          (focus)="loadDropdownOptions(groupField.fieldName, groupField.foreginKey)"
                          (change)="onDropdownChange($event, groupField.fieldName, k, l, field.Group)">
                          <option *ngFor="let option of dropdownOptions[groupField.fieldName]" [value]="option.ROW_ID">
                            <ng-container *ngFor="let key of getKeys(option)">
                              {{ option[key] }}&nbsp;
                            </ng-container>
                          </option>
                        </select>

                        <!-- زر الحذف -->
                        <button type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)"
                          class="remove-button"
                          *ngIf="getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode">
                          <i class="fas fa-trash-alt"></i>
                        </button>

                        <!-- زر الإضافة -->
                        <button type="button" (click)="addMultiField(groupField, k, undefined, field.Group)"
                          class="add-button" *ngIf="!isViewMode">
                          <i class="fas fa-plus-circle"></i>
                        </button>
                      </div>
                    </ng-container>
                  </div>
                </div>

                <button type="button" (click)="removeGroup(field.Group, k)" class="remove-button"
                  *ngIf="getGroupArray(field.Group).controls.length > 1 && !isViewMode" title="Remove">
                  <i class="fas fa-trash-alt"></i>
                </button>
                <button type="button" (click)="addGroup(field.Group, k)" class="add-button" *ngIf="!isViewMode" title="Add">
                  <i class="fas fa-plus-circle"></i>
                </button>
                <!-- زر النسخ -->
                <button type="button" (click)="cloneGroup(field.Group, k)" class="add-button" *ngIf="!isViewMode" title="Clone">
                  <!-- <i class="fas fa-plus-circle"></i> -->
                  <i class="fas fa-clone"></i>
                </button>

              </div>
            </div>
          </ng-container>
        </div>
      </div>

    </form>
  </div>
</div>

<ng-template #successMessage>
  <div class="success-message">Record submitted successfully!</div>
</ng-template>