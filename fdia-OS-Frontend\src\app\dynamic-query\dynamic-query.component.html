<div *ngIf="!showTable" class="query-container">
  <h2 class="query-title">Selection Criteria</h2>
  <div *ngFor="let field of criteriaFields" class="criteria-row">
    <label class="field-label">{{ field.selectionField | titlecase }}</label>
    <mat-select [(ngModel)]="formValues[field.selectionField + '_operator']" class="operator-select">
      <mat-option *ngIf="field.operator" [value]="field.operator">{{ field.operator }}</mat-option>
    </mat-select>
    <input
      matInput
      *ngIf="field.type === 'string'"
      [(ngModel)]="formValues[field.selectionField]"
      class="value-input"
      type="text"
    />
    <input
      matInput
      *ngIf="field.type === 'number'"
      [(ngModel)]="formValues[field.selectionField]"
      class="value-input"
      type="number"
    />
  </div>
  <button mat-raised-button color="primary" (click)="submitQuery()" class="submit-button">Submit</button>
</div>

<div *ngIf="showTable" class="results-container">
  <button mat-raised-button color="primary" (click)="downloadPDF()" class="download-button">Download as PDF</button>
  <table mat-table [dataSource]="results" class="mat-elevation-z8">
    <ng-container *ngFor="let column of columns" [matColumnDef]="column">
      <th mat-header-cell *matHeaderCellDef>{{ column | titlecase }}</th>
      <td mat-cell *matCellDef="let element">{{ element[column] }}</td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="columns"></tr>
    <tr mat-row *matRowDef="let row; columns: columns;"></tr>
  </table>
</div>
