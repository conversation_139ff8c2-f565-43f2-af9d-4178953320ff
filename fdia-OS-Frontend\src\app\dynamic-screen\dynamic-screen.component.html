<div *ngIf="!submissionSuccess; else successMessage" class="form-container">
  <!-- Initial Input Section -->
  <div *ngIf="showInitialInput" class="initial-input">
    <form [formGroup]="form">
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div class="form-field">
        <label for="ID">ID</label>
        <input formControlName="ID" id="ID" type="text" required>

        <div class="button-group">
          <button type="button" (click)="loadDataAndBuildForm()" [disabled]="isLoading" class="action-button">
            <span *ngIf="isLoading">Loading...</span>
            <span *ngIf="!isLoading">Load Data</span>
          </button>

          <button type="button" (click)="viewData()" [disabled]="isLoading" class="action-button">
            <span *ngIf="isLoading">Loading...</span>
            <span *ngIf="!isLoading">View Data</span>
          </button>
        </div>
      </div>
    </form>
  </div>

  <!-- Main Form Section -->
  <div *ngIf="!showInitialInput" class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <!-- Button Group at the Top -->
      <div class="button-group">
        <button type="button" (click)="goBack()" class="action-button">Back</button>
        <button type="button" (click)="authorizeRecord()" class="action-button">Auth</button>
        <button type="submit" [disabled]="form.invalid || isViewMode" class="action-button">Submit</button>
      </div>

      <!-- ID Display -->
      <div class="form-field">
        <label>ID</label>
        <p>{{ form.get('ID')?.value }}</p>
      </div>

      <!-- Dynamic Form Fields -->
      <div *ngFor="let field of fields; trackBy: trackByFieldName">
        <!-- Multi-field (Array) handling -->
        <div *ngIf="field.isMulti" [formArrayName]="field.fieldName" class="field-group">
          <h3>{{ field.fieldName }}</h3>
          <ng-container *ngFor="let control of getMultiArray(field.fieldName).controls; let i = index">
            <div [formGroupName]="i" class="form-grid multi-field">
              <label [for]="field.fieldName">{{ field.fieldName }} ({{ i + 1 }})</label>
              <input
                [formControlName]="field.fieldName"
                [id]="field.fieldName + '-' + i"
                type="text"
                [required]="field.mandatory"
                [readonly]="isViewMode">

              <button type="button" (click)="removeMultiField(field.fieldName, i)" class="remove-button" *ngIf="getMultiArray(field.fieldName).controls.length > 1 && !isViewMode">
                <i class="fas fa-trash-alt"></i>
              </button>

              <button type="button" (click)="addMultiField(field, i)" class="add-button" *ngIf="!isViewMode">
                <i class="fas fa-plus-circle"></i>
              </button>
            </div>
          </ng-container>
        </div>

        <!-- Single Field (Non-array) -->
        <div *ngIf="!field.isMulti && !field.Group && field.fieldName !== 'ID'" class="form-field">
          <label [for]="field.fieldName">{{ field.fieldName }}</label>

          <input
            *ngIf="field.type === 'boolean'"
            [formControlName]="field.fieldName"
            [id]="field.fieldName"
            type="checkbox"
            [required]="field.mandatory"
            [readonly]="isViewMode">

          <input
            *ngIf="field.type === 'string'"
            [formControlName]="field.fieldName"
            [id]="field.fieldName"
            type="text"
            [required]="field.mandatory"
            [readonly]="isViewMode">

          <input
            *ngIf="field.type === 'int'"
            [formControlName]="field.fieldName"
            [id]="field.fieldName"
            type="number"
            [required]="field.mandatory"
            [readonly]="isViewMode">

          <div *ngIf="form.get(field.fieldName)?.invalid && form.get(field.fieldName)?.touched">
            <small class="error">This field is required</small>
          </div>
        </div>

        <!-- Group Fields -->
        <div *ngIf="field.Group && isFirstFieldInGroup(field)" [formArrayName]="field.Group" class="field-group">
          <h3>{{ field.Group }}</h3>
          <ng-container *ngFor="let group of getGroupArray(field.Group).controls; let i = index">
            <div [formGroupName]="i" class="form-grid multi-field">
              <div class="group-fields">
                <div class="form-field" *ngFor="let groupField of getFieldsForGroup(field.Group)">
                  <label [for]="groupField.fieldName">{{ groupField.fieldName }}</label>
                  
                  <input
                    *ngIf="groupField.type === 'string'"
                    [formControlName]="groupField.fieldName"
                    [id]="groupField.fieldName"
                    type="text"
                    [required]="groupField.mandatory"
                    [readonly]="isViewMode">

                  <input
                    *ngIf="groupField.type === 'int'"
                    [formControlName]="groupField.fieldName"
                    [id]="groupField.fieldName"
                    type="number"
                    [required]="groupField.mandatory"
                    [readonly]="isViewMode">

                  <input
                    *ngIf="groupField.type === 'boolean'"
                    [formControlName]="groupField.fieldName"
                    [id]="groupField.fieldName"
                    type="checkbox"
                    [required]="groupField.mandatory"
                    [readonly]="isViewMode">

                  <div *ngIf="getGroupArray(field.Group).at(i).get(groupField.fieldName)?.invalid && getGroupArray(field.Group).at(i).get(groupField.fieldName)?.touched">
                    <small class="error">This field is required</small>
                  </div>
                </div>

                <button type="button" (click)="removeGroup(field.Group, i)" class="remove-button" *ngIf="getGroupArray(field.Group).controls.length > 1 && !isViewMode">
                  <i class="fas fa-trash-alt"></i>
                </button>

                <button type="button" (click)="addGroup(field.Group, i)" class="add-button" *ngIf="!isViewMode">
                  <i class="fas fa-plus-circle"></i>
                </button>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </form>
  </div>
</div>

<ng-template #successMessage>
  <div class="success-message">
    Record inserted successfully
  </div>
</ng-template>
