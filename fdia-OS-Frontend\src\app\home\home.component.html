<div class="home-container">
  <mat-toolbar color="primary" class="compact-toolbar">
    <button mat-icon-button class="menu-icon" (click)="toggleSideMenu()">
      <mat-icon [class.rotated]="isSidenavOpen">menu</mat-icon>
    </button>
    <img src="assets/images/offical-logo-2.png" alt="Company Logo" class="logo" />
    <span class="spacer"></span>
    <div class="icon-frame">
      <img src="assets/images/username.png" alt="Icon" />
    </div>
    <div class="user-info">
      <div class="username"><PERSON><PERSON><PERSON></div>
      <div class="company">My Company Name</div>
      
    </div>
    <!-- <button mat-button class="logout-button" (click)="onLogout()">
      <mat-icon>logout</mat-icon>
      Logout
    </button> -->
  </mat-toolbar>

  <mat-sidenav-container class="side-menu-container compact-sidenav-container">
    <mat-sidenav #sidenav mode="side" class="side-menu compact-sidenav" [opened]="false">
      <div *ngFor="let tab of tabs" class="menu-item">
        <p class="compact-menu-item" (click)="onSidebarMenuItemSelected(tab)">
          <mat-icon class="icon-wrapper">{{ getIcon(tab.type) }}</mat-icon>
          {{ tab.application }}
          <mat-icon *ngIf="tab.type === 'menu'" class="submenu-arrow" [class.rotated]="expandedSidebarMenus[tab.application]">
            expand_more
          </mat-icon>
        </p>
        <div *ngIf="tab.type === 'menu' && expandedSidebarMenus[tab.application]" class="submenu-item">
          <div *ngIf="loadingSidebarMenus[tab.application]">Loading...</div>
          <app-submenu 
            *ngFor="let submenu of sidebarMenus[tab.application]"
            [menuItem]="submenu"
            [level]="1"
            (onMenuSelected)="onSidebarMenuItemSelected($event)">
          </app-submenu>
        </div>
      </div>

      <button mat-button class="logout-button" (click)="onLogout()">
        <mat-icon>logout</mat-icon>
        Logout
      </button>
    </mat-sidenav>

    <mat-sidenav-content>
      <div class="tab-container"> 
        <ul class="nav nav-tabs">
          <li *ngFor="let tab of openTabs; let i = index" class="nav-item" [class.active]="i === activeTabIndex">
            <a class="nav-link" (click)="setActiveTab(i)"> 
              <mat-icon class="icon-wrapper">{{ getIcon(tab.type) }}</mat-icon> {{ tab.application }} 
              <button class="close-tab" (click)="closeTab(i)">&times;</button> 
            </a>
          </li>
        </ul>
      </div>

      <div #tabContent class="tab-content"> </div> 
    </mat-sidenav-content>

    
  </mat-sidenav-container>
</div>