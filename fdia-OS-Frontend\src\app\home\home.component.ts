import {
  Component,
  OnInit,
  ViewChild,
  ComponentFactoryResolver,
  ViewContainerRef,
  ComponentRef,ComponentFactory ,ChangeDetectorRef 
} from '@angular/core';

import { MatSidenavModule, MatSidenav } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { MetadataService } from '../metadata.service';
import { AuthenticationService } from '../authentication.service';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SubmenuComponent } from '../submenu/submenu.component';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';
import { DynamicFormComponent } from '../dynamic-form/dynamic-form.component';
import { DynamicScreenComponent } from '../dynamic-screen/dynamic-screen.component';
import { DynamicQueryComponent } from '../dynamic-query/dynamic-query.component';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    SubmenuComponent,
    MatSidenav
  ],
  animations: [
    trigger('tabAnimation', [
      transition(':increment', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateX(20px)' }),
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
          ])
        ], { optional: true })
      ]),
      transition(':decrement', [
        query(':leave', [
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 0, transform: 'translateX(-20px)' }))
          ])
        ], { optional: true })
      ])
    ])
  ]
})
export class HomeComponent implements OnInit {
  tabs: any[] = [];
  sidebarMenus: { [key: string]: any[] } = {};
  tabMenus: { [key: string]: any[] } = {};
  loadingSidebarMenus: { [key: string]: boolean } = {};
  loadingTabMenus: { [key: string]: boolean } = {};
  expandedSidebarMenus: { [key: string]: boolean } = {};
  expandedTabMenus: { [key: string]: { [key: string]: boolean } } = {};

  openTabs: any[] = [];
  activeTabIndex: number = -1;

  @ViewChild('sidenav') sidenav!: MatSidenav;
  isSidenavOpen: boolean = false;

  @ViewChild('tabContent', { read: ViewContainerRef }) tabContent!: ViewContainerRef;

  componentRefs: { [key: number]: ComponentRef<any> } = {}; // Store component references

  constructor(
    private metadataService: MetadataService,
    private authenticationService: AuthenticationService,
    private router: Router,
    private location: Location,
    private componentFactoryResolver: ComponentFactoryResolver,
    private changeDetectorRef: ChangeDetectorRef

  ) {}

  ngOnInit() {
    const profile = JSON.parse(localStorage.getItem('profile') || '{}');
    this.tabs = profile.menus || [];
    console.log('Main Menu Loaded:', this.tabs);

    this.location.subscribe(() => {
      this.onLogout();
    });
  }

  onSidebarMenuItemSelected(menuItem: any) {
    if (menuItem.type === 'menu') {
      this.expandedSidebarMenus[menuItem.application] = !this.expandedSidebarMenus[menuItem.application];
      if (!this.sidebarMenus[menuItem.application]) {
        this.loadSidebarSubmenu(menuItem.application);
      }
    } else {
      this.openTab(menuItem);
    }
  
    // Reinitialize or reorganize layout
    setTimeout(() => {
      this.changeDetectorRef.detectChanges();
    }, 100); // Delay of 100ms
  }
  

  openTab(menuItem: any) {
    const existingTabIndex = this.openTabs.findIndex(
      (tab) => tab.application === menuItem.application && tab.type === menuItem.type
    );
  
    if (existingTabIndex > -1) {
      // If tab already exists, just set it as active
      this.activeTabIndex = existingTabIndex;
    } else {
      // Create a new tab and add to openTabs
      const newTab = { ...menuItem, id: Date.now(), data: {}, formState: null };
      this.openTabs.push(newTab);
      this.activeTabIndex = this.openTabs.length - 1;
    }
  
    // Load the component for the active tab
    this.loadComponent(this.openTabs[this.activeTabIndex]);
  }
  
  loadComponent(tab: any) {
    if (this.componentRefs[tab.id]) {
      // If the component already exists, show it
      this.showComponent(tab.id);
    } else {
      // Create the component and store its reference
      this.createComponent(tab);
    }
  
    // Trigger change detection to ensure the view updates
    this.changeDetectorRef.detectChanges();
  }

  createComponent(tab: any) {
    let componentFactory: ComponentFactory<any>;
    let componentRef: ComponentRef<any>;
  
    switch (tab.type) {
      case 'table':
        componentFactory = this.componentFactoryResolver.resolveComponentFactory(DynamicFormComponent);
        componentRef = this.tabContent.createComponent(componentFactory);
        componentRef.instance.tableName = tab.application;
        break;
      case 'scr':
        componentFactory = this.componentFactoryResolver.resolveComponentFactory(DynamicScreenComponent);
        componentRef = this.tabContent.createComponent(componentFactory);
        componentRef.instance.screenName = tab.application;
        break;
      case 'qur':
        componentFactory = this.componentFactoryResolver.resolveComponentFactory(DynamicQueryComponent);
        componentRef = this.tabContent.createComponent(componentFactory);
        componentRef.instance.queryName = tab.application;
        break;
      default:
        console.error('Unknown tab type:', tab.type);
        return;
    }
  
    // Store the component reference and initialize data
    this.componentRefs[tab.id] = componentRef;
    componentRef.instance.data = tab.data;
  
    // Listen for data changes to keep the state updated
    componentRef.instance.dataChange.subscribe((updatedData: any) => {
      tab.data = updatedData;
    });
  
    // Hide all other components and show the new one
    this.showComponent(tab.id);
  }


  showComponent(tabId: number) {
    // Hide all components
    for (const key in this.componentRefs) {
      this.componentRefs[key].location.nativeElement.style.display = 'none';
    }
  
    // Show the active component
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].location.nativeElement.style.display = 'block';
    }
  }
  

  setActiveTab(index: number) {
    this.activeTabIndex = index;
    this.showComponent(this.openTabs[index].id);
  }
  

  closeTab(index: number) {
    const tabId = this.openTabs[index].id;
    this.openTabs.splice(index, 1);

    // Destroy the component and remove its reference
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].destroy();
      delete this.componentRefs[tabId];
    }

    if (this.openTabs.length === 0) {
      this.activeTabIndex = -1;
    } else if (this.activeTabIndex >= this.openTabs.length) {
      this.activeTabIndex = this.openTabs.length - 1;
    }

    if (this.activeTabIndex !== -1) {
      this.showComponent(this.openTabs[this.activeTabIndex].id);
    }
  }

  loadSidebarSubmenu(application: string) {
    this.loadingSidebarMenus[application] = true;

    this.metadataService.getMenu(application).subscribe(
      (response) => {
        this.sidebarMenus[application] = response?.menus || [];
        this.loadingSidebarMenus[application] = false;
      },
      (error) => {
        console.error(`Error fetching sidebar submenu for ${application}:`, error);
        this.loadingSidebarMenus[application] = false;
      }
    );
  }

  loadTabSubmenu(application: string) {
    this.loadingTabMenus[application] = true;

    this.metadataService.getMenu(application).subscribe(
      (response) => {
        this.tabMenus[application] = response?.menus || [];
        this.loadingTabMenus[application] = false;
      },
      (error) => {
        console.error(`Error fetching tab submenu for ${application}:`, error);
        this.loadingTabMenus[application] = false;
      }
    );
  }

  toggleSideMenu() {
    this.sidenav.toggle();
    this.isSidenavOpen = !this.isSidenavOpen;
  }

  getIcon(menuType: string): string {
    switch (menuType) {
      case 'menu':
        return 'list';
      case 'qur':
        return 'query_stats';
      case 'scr':
        return 'screen_share';
      case 'table':
        return 'table_chart';
      default:
        return 'info';
    }
  }

  onLogout() {
    this.authenticationService.logout().subscribe(
      () => {
        localStorage.clear();
        sessionStorage.clear();
        this.router.navigate(['/login']);
      },
      (error) => {
        console.error('Logout failed:', error);
      }
    );
  }
}