.login-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #2d4f86, #34a853); // Blue to green gradient inspired by the logo

  .login-container {
    width: 100%;
    max-width: 400px;
    padding: 40px;
    background-color: #ffffff;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    text-align: center;
    animation: fadeIn 1s ease-in-out;

    .logo {
      width: 150px;
      margin-bottom: 20px;
    }

    h2 {
      font-size: 2em;
      font-weight: bold;
      margin-bottom: 20px;
      color: #2d4f86; // Lo<PERSON>'s blue for title color
    }

    form {
      display: flex;
      flex-direction: column;
      align-items: center;

      label {
        margin-bottom: 5px;
        font-size: 0.9em;
        color: #666666;
        align-self: flex-start;
      }

      input[type="text"],
      input[type="password"] {
        width: 100%;
        padding: 12px;
        margin-bottom: 15px;
        border: 1px solid #cccccc;
        border-radius: 8px;
        font-size: 1em;
        color: #333333;
        background-color: #f9f9f9;
        transition: border-color 0.3s;
        &:focus {
          border-color: #2d4f86;
          outline: none;
        }
      }

      .remember-me {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        width: 100%;
        label {
          margin-left: 5px;
          font-size: 0.9em;
          color: #666666;
        }
      }

      button {
        width: 100%;
        padding: 12px;
        background-color: #34a853; // Green from the logo
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 1em;
        font-weight: bold;
        text-transform: uppercase;
        transition: background-color 0.3s;
        &:hover {
          background-color: #2d4f86; // Logo's blue for hover effect
        }
      }

      .forgot-password {
        text-align: center;
        margin-top: 15px;
        color: #2d4f86;
        font-size: 0.9em;
        font-weight: bold;
        text-decoration: underline;
        &:hover {
          color: #34a853;
        }
      }
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.error-message {
  color: #d9534f; /* Red color for error message */
  font-size: 0.9em;
  margin-bottom: 15px;
  font-weight: bold;
  text-align: center;
}