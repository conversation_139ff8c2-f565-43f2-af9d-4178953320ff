// login.component.ts
import { Component } from '@angular/core';
import { AuthenticationService } from '../authentication.service';
import { FormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',  
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [FormsModule, NgIf]
})
export class LoginComponent {
  errorMessage: string = '';

  constructor(
    private authenticationService: AuthenticationService, 
    private router: Router
  ) {}

  onSubmit(form: any) {
    this.errorMessage = '';
    this.authenticationService.login(form.value).subscribe({
      next: (response: any) => {
        if (response.success) {
          const username = form.value.username;
          this.authenticationService.getProfile(username).subscribe(
            (profile: any) => {
              localStorage.setItem('profile', JSON.stringify(profile));
              this.router.navigate(['/home']);
            },
            (error) => {
              console.error('Failed to fetch profile:', error);
            }
          );
        } else {
          this.errorMessage = response.message || 'Invalid username or password.';
        }
      },
      error: (error) => {
        const apiErrorMessage = error.error?.message || 'Login failed. Please try again later.';
        
        if (apiErrorMessage === 'No password set for this user') {
          localStorage.setItem('canAccessSetPassword', 'true');
          sessionStorage.setItem('tempUsername', form.value.username); // Store username
          this.router.navigate(['/set-password']); 
        } else {
          this.errorMessage = apiErrorMessage;
        }
      }
    });
  }
}