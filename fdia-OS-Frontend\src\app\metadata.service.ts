import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MetadataService {

  private menuUrl = 'http://localhost:5553/api/menu/'; // Base URL for menu API
  private tableMetadataUrl = 'http://localhost:5553/api/merged/'; // Base URL for table metadata
  private screenMetadataUrl = 'http://localhost:5553/api/merged/'; // Base URL for table metadata

  constructor(private http: HttpClient) { }

  getMenu(application: string): Observable<any> {
    return this.http.get(`${this.menuUrl}${application}`, { withCredentials: true });
  }
  getTableMetadata(tableName: string): Observable<any> {
    return this.http.get(`${this.tableMetadataUrl}${tableName}/metadata`, { withCredentials: true });
  }
  getScreenMetadata(tableName: string): Observable<any> {
    return this.http.get(`${this.screenMetadataUrl}${tableName}/metadata`, { withCredentials: true });
  }
  getScreen(screenId: string): Observable<any> {
    // TODO: Implement screen fetching logic
    return new Observable(); 
  }
  getCriteriaFields(queryName: string): Observable<any[]> {
    return this.http.get<any[]>(`http://localhost:5553/api/query-builder/${queryName}`, { withCredentials: true });
  }
}