<div class="set-password-page">
  <div class="set-password-container">
    <img src="assets/images/offical-logo-2.png" alt="Ultimate Solutions Logo" class="logo">
    <h2>Set Your Password</h2>

    <div *ngIf="errorMessage" class="error-message">{{ errorMessage }}</div>
    <div *ngIf="successMessage" class="success-message">{{ successMessage }}</div>

    <div *ngIf="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div> 
    </div>

    <form (ngSubmit)="onSubmit()">
      <div>
        <label for="newPassword">New Password</label>
        <input type="password" id="newPassword" [(ngModel)]="newPassword" name="newPassword" required>
      </div>
      
      <div>
        <label for="confirmPassword">Confirm Password</label>
        <input type="password" id="confirmPassword" [(ngModel)]="confirmPassword" name="confirmPassword" required>
      </div>

      <button type="submit">Set Password</button>
    </form>
  </div>
</div>