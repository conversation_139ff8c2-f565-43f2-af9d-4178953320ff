/* General Container Styles */
.home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Too<PERSON><PERSON> Styles with White to Blue Gradient */
.compact-toolbar {
  height: 72px;
  padding: 0 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: #ffffff; 
  //background: linear-gradient(to left, #2d4f86, rgba(255, 255, 255, 0.8)); 
}

/* Logo Styles */
.logo {
  height: 30px;
  margin-left: 10px;
  vertical-align: middle;
}

/* Spacer for Right Alignment */
.spacer {
  flex: 1 1 auto;
}

/* Menu Icon Animation */
.menu-icon {
  .mat-icon {
    transition: transform 0.3s ease;
    color: #00a651; 
  }

  &.rotated {
    transform: rotate(90deg);
  }
}

/* Sidenav Styles */
.compact-sidenav-container {
  flex: 1;
}

.compact-sidenav {
  width: 220px;
  padding: 10px;
 // background-color: #f5f5f5;
  background-color: #FAFAFA;
  color: #2d4f86;
}

/* Sidebar Menu Item Styles */
.compact-menu-item {
  font-size: 13px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s, color 0.2s;
  color: #2d4f86; 

  &:hover {
    background-color: #e0e0e0;
    color: #00a651; 
  }

  &.active {
    background-color: #4a90e2; 
    color: #ffffff;
  }
}

.mat-icon {
  margin-right: 8px;
}

/* Sidebar Submenu Item Styles */
.compact-submenu-item {
  font-size: 13px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  padding-left: 20px; /* Indentation for submenu items */
  border-radius: 6px;
  transition: background-color 0.2s, color 0.2s;
  color: #2d4f86; 

  &:hover {
    background-color: #e0e0e0;
    color: #00a651; 
  }

  &.active {
    background-color: #4a90e2; 
    color: #ffffff;
  }
}

/* Chrome-like Tabs Navigation Styles */
.tab-container {
  background-color: #f0f0f0; /* Match Chrome's light gray */
  margin-top: 0;   /* Remove any default margin on the tab container */
  padding-top: 0; /* Remove any default padding that might be creating space */
}

.nav-tabs {
  display: flex;
  overflow-x: auto; /* Enable horizontal scrolling if needed */
  padding: 3px; /* Reduced padding */
  border-bottom: none; /* Remove the default bottom border */
}

.nav-item {
  margin-bottom: -1px; /* Align tabs seamlessly */
}

.nav-link {
  color: #333; 
  border: none; /* Remove existing borders */
  border-radius: 3px 3px 0 0; /* Chrome's rounded corners */
  background-color: #e8e8e8; /* Chrome's inactive tab color */
  padding: 4px 8px; /* Reduce padding */
  margin-right: 1px; /* Reduce margin */ 
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s; 
  position: relative; /* For positioning the close button */
  white-space: nowrap; /* Prevent tab titles from wrapping */
  display: flex; /* Make nav-link a flex container */
  align-items: center; /* Vertically align content */
  font-size: 12px; /* Smaller font size */
  

  &:hover {
    background-color: #ddd; 
  }

  &.active {
    background-color: #f0f0f0; /* Use a lighter background for inactive tabs */
    color: #333; /* Darker text for inactive tabs */
    z-index: 1; 
    box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.1); 
  }
}

.close-tab {
  border: none;
  background-color: transparent;
  margin-left: 4px; /* Reduced margin */
  font-size: 20px; /* Smaller close button */
  color: #666;
  cursor: pointer;
  opacity: 0.5; /* Slightly faded out by default */
  transition: opacity 0.3s; 

  &:hover {
    opacity: 1; 
  }
}

/* Tab Content Area with Animation */
.tab-content {
  padding: 0px; 
  animation: fadeIn 0.3s ease-in-out; 
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Styles for Small Screens */
@media (max-width: 768px) {
  .compact-nav { 
    display: none; 
  }

  .compact-toolbar {
    justify-content: space-between; 
    margin-bottom: 0; /* Remove any default margin on the toolbar */

  }

  .compact-tab-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .compact-menu-item {
    font-size: 10px;
    padding: 4px;
  }
}

/* Logout Button */
.logout-button {
  color: #0a0a0a !important; /* Override any default button color */
  font-weight: bold;
  transition: color 0.3s;

  &:hover {
    color: #4a90e2; 
  }
}

.search-bar {
  margin-right: 20px;
  width: 200px;
}

.nav-tabs .nav-item .nav-link {
  /* ... other styles ... */
  background-color: #e0e0e0; /* Slightly darker gray for inactive tabs */ 
}

.nav-tabs .nav-item.active .nav-link {
  /* ... other styles ... */
  background-color: #fff; /* White for the active tab */
  border-bottom: 2px solid #ffffff; /* Add a distinct border to the active tab */
}


.tab-container {
  padding: 8px 24px 0px;
  background: #ffffff;
  height: 40px;
}

.nav-tabs {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #0DB14B;
 // padding: 0;
  margin: 0;
  list-style: none;
  height: 32px;
}

.nav-item {
  //display: flex;
  align-items: center;
}

.nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 25px;
  padding: 8px 32px 4px;
  gap: 10px;
  border-radius: 12px 12px 0px 0px;
  text-decoration: none;
  color: #222222;
  background: #FAFAFA;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  position: relative;
  cursor: pointer;
}

.nav-item.active .nav-link {
  background: #ffffff;
  border: 1px solid #0DB14B;
  border-bottom: none;
}

.tab-content-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tab-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
  background: #E7F7ED;
  border-radius: 50%;
  font-size: 16px;
}

.tab-icon.inactive {
  background: transparent;
}

.tab-text {
  white-space: nowrap;
}

.close-tab {
  background: none;
  border: none;
  font-size: 25px;
  line-height: 1;
  cursor: pointer;
  color: #4D4D4D;
  margin-left: 8px;
}


//----------------------------------------------


.icon-frame img {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 50%;
  padding-right: 10px;
}


.username {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  color: #222222;
  display: flex;
  align-items: center;
  height: 21px;
  width: 116px;
}

.company {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #4E4E4E;
  flex-grow: 1;
}

//------------------------------

.submenu-arrow {
  transition: transform 0.3s ease;
}
.submenu-arrow.rotated {
  transform: rotate(180deg);
}


.compact-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 12px;
  cursor: pointer;
}

.icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background-color: #E7F7ED; /* خلفية خفيفة */
  border-radius: 50%;
  flex-shrink: 0;
  color: #0DB14B;
}

.icon-wrapper mat-icon {
  color: #0DB14B; /* لون الأيقونة أخضر */
  font-size: 20px;
}

.text-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.application-name {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #222222;
}

.company-name {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #4E4E4E;
}

.submenu-arrow {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.submenu-arrow.rotated {
  transform: rotate(180deg);
}

.submenu-item {
  background-color: #ffffff;
  border-radius: 8px;
}