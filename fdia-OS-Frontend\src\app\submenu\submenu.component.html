<div class="submenu-item" [style.paddingLeft.px]="level * 20">
  <p class="compact-submenu-item" [ngClass]="'level-' + level" (click)="toggleSubmenu($event)">
    <span>
      <mat-icon>{{ getIcon(menuItem.type) }}</mat-icon>
      {{ menuItem.application }}
    </span>
    <mat-icon *ngIf="menuItem.type === 'menu'" class="submenu-arrow" [class.rotated]="expanded">
      expand_more
    </mat-icon>
  </p>

  <div *ngIf="loading">Loading...</div>

  <div *ngIf="expanded && !loading">
    <app-submenu 
      *ngFor="let submenu of submenuItems"
      [menuItem]="submenu"
      [level]="level + 1"
      (onMenuSelected)="selectMenuItem($event)"> 
    </app-submenu>
  </div>
</div>
